import type { BookedSlot } from '@/modules/booking/booking.apis'
import { bookingAPIs } from '@/modules/booking/booking.apis'
import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'

interface BookingSlotsState {
  // State
  bookedSlots: BookedSlot[]
  selectedDate: string | null
  bookingPageId: string | null
  isLoading: boolean
  error: string | null

  // Actions
  setBookingPageId: (id: string) => void
  setSelectedDate: (date: string) => void
  loadBookedSlots: (bookingPageId: string, date: string) => Promise<void>
  clearBookedSlots: () => void
  isSlotBooked: (field: string, time: string) => boolean
  getSlotStatus: (field: string, time: string) => 'available' | 'pending' | 'confirmed' | 'cancelled'
}

export const useBookingSlotsStore = create<BookingSlotsState>()(
  immer((set, get) => ({
    // Initial state
    bookedSlots: [],
    selectedDate: null,
    bookingPageId: null,
    isLoading: false,
    error: null,

    // Set booking page ID
    setBookingPageId: (id: string) => {
      set((state) => {
        state.bookingPageId = id
      })
    },

    // Set selected date
    setSelectedDate: (date: string) => {
      set((state) => {
        state.selectedDate = date
      })
    },

    // Load booked slots for a specific date
    loadBookedSlots: async (bookingPageId: string, date: string) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await bookingAPIs.getBookedSlots({
          bookingPageId,
          date,
        })

        if (response.status?.success) {
          set((state) => {
            state.bookedSlots = response.data?.bookedSlots || []
            state.selectedDate = date
            state.bookingPageId = bookingPageId
            state.isLoading = false
          })
        } else {
          throw new Error(response.status?.message || 'Failed to load booked slots')
        }
      } catch (error) {
        console.error('Error loading booked slots:', error)
        set((state) => {
          state.error = error instanceof Error ? error.message : 'Unknown error'
          state.isLoading = false
          state.bookedSlots = []
        })
      }
    },

    // Clear booked slots
    clearBookedSlots: () => {
      set((state) => {
        state.bookedSlots = []
        state.selectedDate = null
        state.error = null
      })
    },

    // Check if a specific slot is booked
    isSlotBooked: (field: string, time: string) => {
      const { bookedSlots } = get()
      return bookedSlots.some(slot =>
        slot.field === field
        && slot.time === time
        && slot.status !== 'cancelled',
      )
    },

    // Get slot status
    getSlotStatus: (field: string, time: string) => {
      const { bookedSlots } = get()
      const slot = bookedSlots.find(slot =>
        slot.field === field && slot.time === time,
      )

      if (!slot || slot.status === 'cancelled') {
        return 'available'
      }

      return slot.status
    },
  })),
)
