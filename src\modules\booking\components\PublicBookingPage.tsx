'use client'

import type { BookingPageItem } from '@/modules/admin/apis/booking-page.api'
import type { Block } from '@/modules/admin/create-booking-page/types/blocks'
import { BlockRenderer } from '@/modules/admin/create-booking-page/components/BlockRenderer'
import { useSelectedDate } from '@/modules/admin/create-booking-page/components/blocks/AvailabilityCalendar'
import { useBookingPageDetailStore } from '@/modules/admin/stores/booking-page-detail.store'
import React, { useEffect } from 'react'

interface PublicBookingPageProps {
  bookingPage: BookingPageItem
}

/**
 * PublicBookingPage Component
 *
 * Displays a public booking page using the block renderer from the admin section
 */
const PublicBookingPage: React.FC<PublicBookingPageProps> = ({ bookingPage }) => {
  const setBookingPage = useBookingPageDetailStore(state => state.setBookingPage)
  const selectedDate = useSelectedDate()

  // Apply theme settings to the page
  const pageStyle = {
    'fontFamily': bookingPage.theme.fontFamily,
    '--primary-color': bookingPage.theme.primaryColor,
    'backgroundColor': bookingPage.theme.backgroundColor || 'white',
    'color': bookingPage.theme.textColor || 'inherit',
  } as React.CSSProperties

  useEffect(() => {
    setBookingPage(bookingPage)
  }, [])

  useEffect(() => {
    if (selectedDate) {
      console.log('Selected date in public booking page:', selectedDate)
    }
  }, [selectedDate])

  return (
    <div className="min-h-screen" style={pageStyle}>
      <div className="mx-auto pt-0 pb-4">
        <BlockRenderer
          blocks={bookingPage.blocks as Block[]}
          layout={bookingPage.theme.layout}
        />
      </div>
    </div>
  )
}

export default PublicBookingPage
