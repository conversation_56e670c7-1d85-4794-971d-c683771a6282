import { describe, it, expect, beforeEach, vi } from 'vitest'
import { useBookingSlotsStore } from './booking-slots.store'
import { bookingAPIs } from '../booking.apis'

// Mock the booking APIs
vi.mock('../booking.apis', () => ({
  bookingAPIs: {
    getBookedSlots: vi.fn(),
  },
}))

describe('useBookingSlotsStore', () => {
  beforeEach(() => {
    // Reset the store before each test
    useBookingSlotsStore.getState().clearBookedSlots()
    vi.clearAllMocks()
  })

  it('should set booking page ID and load slots if date is selected', async () => {
    const mockResponse = {
      status: { success: true },
      data: {
        bookedSlots: [
          {
            field: 'field-1',
            time: '10:00',
            date: '2024-01-15',
            bookingId: 'booking-1',
            status: 'confirmed' as const,
          },
        ],
      },
    }

    vi.mocked(bookingAPIs.getBookedSlots).mockResolvedValue(mockResponse)

    const store = useBookingSlotsStore.getState()
    
    // Set a selected date first
    store.setSelectedDate('2024-01-15')
    
    // Then set booking page ID - this should trigger loading slots
    await store.setBookingPageId('booking-page-1')

    expect(bookingAPIs.getBookedSlots).toHaveBeenCalledWith({
      bookingPageId: 'booking-page-1',
      date: '2024-01-15',
    })

    const state = useBookingSlotsStore.getState()
    expect(state.bookingPageId).toBe('booking-page-1')
    expect(state.bookedSlots).toHaveLength(1)
    expect(state.bookedSlots[0].field).toBe('field-1')
  })

  it('should set selected date and load slots if booking page ID is set', async () => {
    const mockResponse = {
      status: { success: true },
      data: {
        bookedSlots: [
          {
            field: 'field-2',
            time: '14:00',
            date: '2024-01-16',
            bookingId: 'booking-2',
            status: 'pending' as const,
          },
        ],
      },
    }

    vi.mocked(bookingAPIs.getBookedSlots).mockResolvedValue(mockResponse)

    const store = useBookingSlotsStore.getState()
    
    // Set booking page ID first
    await store.setBookingPageId('booking-page-2')
    
    // Then set selected date - this should trigger loading slots
    await store.setSelectedDateAndLoadSlots('2024-01-16')

    expect(bookingAPIs.getBookedSlots).toHaveBeenCalledWith({
      bookingPageId: 'booking-page-2',
      date: '2024-01-16',
    })

    const state = useBookingSlotsStore.getState()
    expect(state.selectedDate).toBe('2024-01-16')
    expect(state.bookedSlots).toHaveLength(1)
    expect(state.bookedSlots[0].status).toBe('pending')
  })

  it('should set selected date from Date object and load slots', async () => {
    const mockResponse = {
      status: { success: true },
      data: {
        bookedSlots: [],
      },
    }

    vi.mocked(bookingAPIs.getBookedSlots).mockResolvedValue(mockResponse)

    const store = useBookingSlotsStore.getState()
    
    // Set booking page ID first
    await store.setBookingPageId('booking-page-3')
    
    // Set selected date from Date object
    const testDate = new Date('2024-01-17T10:00:00Z')
    await store.setSelectedDateFromDateObject(testDate)

    expect(bookingAPIs.getBookedSlots).toHaveBeenCalledWith({
      bookingPageId: 'booking-page-3',
      date: '2024-01-17',
    })

    const state = useBookingSlotsStore.getState()
    expect(state.selectedDate).toBe('2024-01-17')
  })

  it('should check if slot is booked correctly', () => {
    const store = useBookingSlotsStore.getState()
    
    // Manually set some booked slots
    store.loadBookedSlots = vi.fn()
    useBookingSlotsStore.setState({
      bookedSlots: [
        {
          field: 'field-1',
          time: '10:00',
          date: '2024-01-15',
          bookingId: 'booking-1',
          status: 'confirmed',
        },
        {
          field: 'field-1',
          time: '11:00',
          date: '2024-01-15',
          bookingId: 'booking-2',
          status: 'cancelled',
        },
      ],
    })

    const state = useBookingSlotsStore.getState()
    
    // Should return true for confirmed booking
    expect(state.isSlotBooked('field-1', '10:00')).toBe(true)
    
    // Should return false for cancelled booking
    expect(state.isSlotBooked('field-1', '11:00')).toBe(false)
    
    // Should return false for non-existent booking
    expect(state.isSlotBooked('field-1', '12:00')).toBe(false)
  })

  it('should get slot status correctly', () => {
    const store = useBookingSlotsStore.getState()
    
    // Manually set some booked slots
    useBookingSlotsStore.setState({
      bookedSlots: [
        {
          field: 'field-1',
          time: '10:00',
          date: '2024-01-15',
          bookingId: 'booking-1',
          status: 'confirmed',
        },
        {
          field: 'field-1',
          time: '11:00',
          date: '2024-01-15',
          bookingId: 'booking-2',
          status: 'pending',
        },
      ],
    })

    const state = useBookingSlotsStore.getState()
    
    expect(state.getSlotStatus('field-1', '10:00')).toBe('confirmed')
    expect(state.getSlotStatus('field-1', '11:00')).toBe('pending')
    expect(state.getSlotStatus('field-1', '12:00')).toBe('available')
  })
})
