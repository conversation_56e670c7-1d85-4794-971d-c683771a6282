import type { AvailabilityGridProps } from '../types'
import { cn } from '@/libs/utils'
import { useBookingSlotsStore } from '@/modules/booking/stores/booking-slots.store'
import { MapPin } from 'lucide-react'
import React from 'react'
import { useIsSlotSelected, useToggleBookingSlot } from '../store/availabilityCalendarStore'

/**
 * Availability grid component for displaying time slots and fields
 */
export const AvailabilityGridBase: React.FC<AvailabilityGridProps> = ({
  timeSlots,
  displayFields,
  selectedDate,
}) => {
  // Get helpers from the stores
  const isSlotSelected = useIsSlotSelected()
  const toggleBookingSlot = useToggleBookingSlot()
  const { getSlotStatus } = useBookingSlotsStore()

  // Create a memoized toggle handler that includes the selected date
  const handleToggleBookingSlot = (fieldId: string, time: string) => {
    // Only allow selection if slot is available
    const slotStatus = getSlotStatus(fieldId, time)

    if (slotStatus === 'available') {
      toggleBookingSlot(fieldId, time, selectedDate)
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-sm overflow-x-auto">
      <div className="min-w-[600px]">
        {/* Header with field names */}
        <div className="grid border-b" style={{ gridTemplateColumns: `repeat(${displayFields.length}, 1fr)` }}>
          {displayFields.map(field => (
            <div
              key={field.id}
              className="p-2 text-center font-medium border-r last:border-r-0 bg-gray-50"
            >
              <div>{field.name}</div>
            </div>
          ))}
        </div>

        {/* Time slots */}
        <div>
          {timeSlots.map(slot => (
            <div
              key={`slot-${slot.time}`}
              className="grid border-b"
              style={{ gridTemplateColumns: `repeat(${displayFields.length}, 1fr)` }}
            >
              {/* Availability cells for each field */}
              {displayFields.map((field) => {
                // Simulate availability (in a real app, this would come from backend)
                // const isAvailable = Math.random() > 0.3
                const isAvailable = true
                const isSelected = isSlotSelected(field.id, slot.time)

                return (
                  <div
                    key={`${field.id}-${slot.time}`}
                    role="button"
                    tabIndex={0}
                    className={cn(
                      'p-2 h-20 border-r last:border-r-0 flex items-center justify-center cursor-pointer transition-all hover:shadow-sm',
                      !isAvailable && 'bg-gray-50 cursor-not-allowed',
                      isAvailable && !isSelected && 'bg-green-50/30 hover:bg-green-50',
                      isSelected && 'bg-blue-50 hover:bg-blue-100 ring-1 ring-blue-200',
                    )}
                    onClick={() => {
                      if (isAvailable) {
                        handleToggleBookingSlot(field.id, slot.time)
                      }
                    }}
                  >
                    <div className="flex flex-col items-center justify-center w-full">
                      {/* Time slot indicator */}
                      <div className="text-xs font-medium bg-gray-100 rounded-full px-2 py-0.5 mb-1">{slot.time}</div>

                      {/* Status indicator with icon and color */}
                      <div className={cn(
                        'flex items-center gap-1.5 font-medium text-sm',
                        isSelected && 'text-blue-600',
                        isAvailable && !isSelected && 'text-green-600',
                        !isAvailable && 'text-gray-400',
                      )}
                      />

                      {/* Field location indicator */}
                      <div className="flex items-center gap-1 text-xs text-gray-600 mt-1.5">
                        <MapPin className="h-3 w-3" />
                        <span>{field.name}</span>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export const AvailabilityGrid = React.memo(AvailabilityGridBase)
