/* eslint-disable react/no-array-index-key */
import type { Block } from '../types/blocks'
import type { AvailabilityCalendarBlockProps } from './blocks/AvailabilityCalendarBlock'
import type { BannerBlockProps } from './blocks/BannerBlock'
import type { BookingFormBlockProps } from './blocks/BookingFormBlock'
import type { BookingTicketBlockProps } from './blocks/BookingTicketBlock'
import type { DescriptionBlockProps } from './blocks/DescriptionBlock'
import type { InfoBlockProps } from './blocks/InfoBlock'
import type { MapBlockProps } from './blocks/MapBlock'
import React from 'react'
import { LayoutType } from '../types/theme'
// Import block components
import { AvailabilityCalendarBlockComponent } from './blocks/AvailabilityCalendarBlock'
import { BannerBlockComponent } from './blocks/BannerBlock'
import { BookingFormBlockComponent } from './blocks/BookingFormBlock'
import { BookingTicketBlockComponent } from './blocks/BookingTicketBlock'
import { DescriptionBlockComponent } from './blocks/DescriptionBlock'
import { InfoBlockComponent } from './blocks/InfoBlock'
import { MapBlockComponent } from './blocks/MapBlock'

// Component to render a single block based on its type
const SingleBlockRenderer: React.FC<{ block: Block, isPreview?: boolean }> = ({ block, isPreview = false }) => {
  switch (block.type) {
    case 'banner':
      return <BannerBlockComponent {...block.data as BannerBlockProps} />

    case 'info':
      return <InfoBlockComponent {...block.data as InfoBlockProps} />

    case 'description':
      return <DescriptionBlockComponent {...block.data as DescriptionBlockProps} />

    case 'booking_form':
      return <BookingFormBlockComponent {...block.data as BookingFormBlockProps} />

    case 'map':
      return <MapBlockComponent {...block.data as MapBlockProps} />

    case 'availability_calendar':
      return <AvailabilityCalendarBlockComponent {...block.data as AvailabilityCalendarBlockProps} />

    case 'booking_ticket':
      return <BookingTicketBlockComponent {...block.data as BookingTicketBlockProps} isPreview={isPreview} />

    default:
      return null
  }
}

interface BlockRendererProps {
  blocks: Block[]
  layout?: LayoutType
  isPreview?: boolean
}

/**
 * BlockRenderer Component
 *
 * Renders a list of blocks based on their type
 */
export const BlockRenderer: React.FC<BlockRendererProps> = ({
  blocks,
  layout = LayoutType.VERTICAL,
  isPreview = false,
}) => {
  // For banner-split-map layout, show banner at top, 30-70 split in middle, map at bottom
  if (layout === LayoutType.BANNER_SPLIT_MAP) {
    // Extract the banner block (usually the first one with type 'banner')
    const bannerBlock = blocks.find(block => block.type === 'banner')

    // Extract the map block (usually with type 'map')
    const mapBlock = blocks.find(block => block.type === 'map')

    // Get the remaining blocks (excluding banner and map)
    const remainingBlocks = blocks.filter(block =>
      block !== bannerBlock && block !== mapBlock)

    // Find info/booking block for left column (30%)
    const infoBlock = remainingBlocks.filter(block =>
      block.type === 'info' || block.type === 'booking_form' || block.type === 'booking_ticket')

    // Find calendar block for right column (70%)
    const calendarBlock = remainingBlocks.find(block =>
      block.type === 'availability_calendar')

    // Other blocks that don't fit the specific categories
    const otherBlocks = remainingBlocks.filter(block =>
      !infoBlock.includes(block) && block !== calendarBlock)

    return (
      <div className="space-y-6">
        {/* Banner at the top (full width) */}
        {bannerBlock && <SingleBlockRenderer key="banner" block={bannerBlock} isPreview={isPreview} />}

        {/* Middle row with 30-70 split */}
        <div className="grid grid-cols-1 md:grid-cols-10 gap-6">
          {/* Left column - 30% */}
          <div className="md:col-span-3 space-y-6">
            {infoBlock && infoBlock?.map((item, index) => <SingleBlockRenderer key={index} block={item} isPreview={isPreview} />) }
          </div>

          {/* Right column - 70% */}
          <div className="md:col-span-7 space-y-6">
            {calendarBlock && <SingleBlockRenderer key="calendar" block={calendarBlock} isPreview={isPreview} />}
          </div>
        </div>

        {/* Map at the bottom (full width) */}
        {mapBlock && <SingleBlockRenderer key="map" block={mapBlock} isPreview={isPreview} />}

        {/* Any other blocks */}
        {otherBlocks.length > 0 && (
          <div className="space-y-6">
            {otherBlocks.map((block, index) => (
              <SingleBlockRenderer key={`other-${index}`} block={block} isPreview={isPreview} />
            ))}
          </div>
        )}
      </div>
    )
  }

  // For sport-basic layout, show banner at the top and split the rest into 2 columns
  if (layout === LayoutType.SPORT_BASIC) {
    // Extract the banner block (usually the first one)
    const bannerBlock = blocks.find(block => block.type === 'banner') || blocks[0]

    // Get the remaining blocks (excluding the banner)
    const remainingBlocks = blocks.filter(block => block !== bannerBlock)

    // Split remaining blocks into left and right columns
    const leftBlocks = remainingBlocks.filter((_, index) => index % 2 === 0)
    const rightBlocks = remainingBlocks.filter((_, index) => index % 2 === 1)

    return (
      <div className="space-y-6">
        {/* Banner at the top */}
        {bannerBlock && <SingleBlockRenderer key="banner" block={bannerBlock} isPreview={isPreview} />}

        {/* Two columns below */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-6">
            {leftBlocks.map((block, index) => (
              <SingleBlockRenderer key={`left-${index}`} block={block} isPreview={isPreview} />
            ))}
          </div>
          <div className="space-y-6">
            {rightBlocks.map((block, index) => (
              <SingleBlockRenderer key={`right-${index}`} block={block} isPreview={isPreview} />
            ))}
          </div>
        </div>
      </div>
    )
  }

  // For 2-column layout, split blocks into left and right columns
  if (layout === LayoutType.TWO_COLUMN) {
    const leftBlocks = blocks.filter((_, index) => index % 2 === 0)
    const rightBlocks = blocks.filter((_, index) => index % 2 === 1)

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-6">
          {leftBlocks.map((block, index) => (
            <SingleBlockRenderer key={`left-${index}`} block={block} isPreview={isPreview} />
          ))}
        </div>
        <div className="space-y-6">
          {rightBlocks.map((block, index) => (
            <SingleBlockRenderer key={`right-${index}`} block={block} isPreview={isPreview} />
          ))}
        </div>
      </div>
    )
  }

  // Default vertical layout
  return (
    <div className="space-y-6 max-w-3xl mx-auto pt-0 p-4 bg-white min-h-[500px]">
      {blocks.map((block: any, index) => (
        <SingleBlockRenderer key={`${block.type}-${index}`} block={block} isPreview={isPreview} />
      ))}
    </div>
  )
}
