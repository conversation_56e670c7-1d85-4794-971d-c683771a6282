import type { LegendProps } from '../types'
import React from 'react'

/**
 * Legend component for displaying availability status
 */
export const Legend: React.FC<LegendProps> = ({ show }) => {
  if (!show) {
    return null
  }

  return (
    <div className="mt-4 pt-4 border-t">
      <h4 className="text-sm font-medium mb-2">Ch<PERSON> thích</h4>
      <div className="flex flex-wrap gap-4">
        <div className="flex items-center">
          <div className="w-4 h-4 bg-white border rounded mr-2"></div>
          <span className="text-sm">Còn trống</span>
        </div>
        <div className="flex items-center">
          <div className="w-4 h-4 bg-blue-100 rounded mr-2"></div>
          <span className="text-sm">Đã chọn</span>
        </div>
        <div className="flex items-center">
          <div className="w-4 h-4 bg-gray-100 rounded mr-2"></div>
          <span className="text-sm">Đã đặt</span>
        </div>
      </div>
    </div>
  )
}
