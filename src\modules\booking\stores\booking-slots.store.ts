import type { BookedSlot } from '@/modules/booking/booking.apis'
import { bookingAPIs } from '@/modules/booking/booking.apis'
import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'

// Helper function to format date to YYYY-MM-DD
const formatDateToString = (date: Date | string): string => {
  if (typeof date === 'string') {
    return date
  }
  return date.toISOString().split('T')[0] || ''
}

interface BookingSlotsState {
  // State
  bookedSlots: BookedSlot[]
  selectedDate: string | null
  bookingPageId: string | null
  isLoading: boolean
  error: string | null

  // Actions
  setBookingPageId: (id: string) => Promise<void>
  setSelectedDate: (date: string) => void
  setSelectedDateAndLoadSlots: (date: string) => Promise<void>
  setSelectedDateFromDateObject: (date: Date) => Promise<void>
  loadBookedSlots: (bookingPageId: string, date: string) => Promise<void>
  clearBookedSlots: () => void
  isSlotBooked: (field: string, time: string) => boolean
  getSlotStatus: (field: string, time: string) => 'available' | 'pending' | 'confirmed' | 'cancelled'
}

export const useBookingSlotsStore = create<BookingSlotsState>()(
  immer((set, get) => ({
    // Initial state
    bookedSlots: [],
    selectedDate: null,
    bookingPageId: null,
    isLoading: false,
    error: null,

    // Set booking page ID and load slots if date is already selected
    setBookingPageId: async (id: string) => {
      const { selectedDate, loadBookedSlots } = get()

      set((state) => {
        state.bookingPageId = id
      })

      // If we have a selected date, load the booked slots for this booking page
      if (selectedDate) {
        await loadBookedSlots(id, selectedDate)
      }
    },

    // Set selected date
    setSelectedDate: (date: string) => {
      set((state) => {
        state.selectedDate = date
      })
    },

    // Set selected date and automatically load booked slots
    setSelectedDateAndLoadSlots: async (date: string) => {
      const { bookingPageId, loadBookedSlots } = get()

      // Set the selected date first
      set((state) => {
        state.selectedDate = date
      })

      // If we have a booking page ID, load the booked slots for this date
      if (bookingPageId) {
        await loadBookedSlots(bookingPageId, date)
      }
    },

    // Set selected date from Date object and automatically load booked slots
    setSelectedDateFromDateObject: async (date: Date) => {
      const dateString = formatDateToString(date)
      const { bookingPageId, loadBookedSlots } = get()

      // Set the selected date first
      set((state) => {
        state.selectedDate = dateString
      })

      // If we have a booking page ID, load the booked slots for this date
      if (bookingPageId) {
        await loadBookedSlots(bookingPageId, dateString)
      }
    },

    // Load booked slots for a specific date
    loadBookedSlots: async (bookingPageId: string, date: string) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await bookingAPIs.getBookedSlots({
          bookingPageId,
          date,
        })

        if (response.status?.success) {
          set((state) => {
            state.bookedSlots = response.data?.bookedSlots || []
            state.selectedDate = date
            state.bookingPageId = bookingPageId
            state.isLoading = false
          })
        } else {
          throw new Error(response.status?.message || 'Failed to load booked slots')
        }
      } catch (error) {
        console.error('Error loading booked slots:', error)
        set((state) => {
          state.error = error instanceof Error ? error.message : 'Unknown error'
          state.isLoading = false
          state.bookedSlots = []
        })
      }
    },

    // Clear booked slots
    clearBookedSlots: () => {
      set((state) => {
        state.bookedSlots = []
        state.selectedDate = null
        state.error = null
      })
    },

    // Check if a specific slot is booked
    isSlotBooked: (field: string, time: string) => {
      const { bookedSlots } = get()
      return bookedSlots.some(slot =>
        slot.field === field
        && slot.time === time
        && slot.status !== 'cancelled',
      )
    },

    // Get slot status
    getSlotStatus: (field: string, time: string) => {
      const { bookedSlots } = get()
      const slot = bookedSlots.find(slot =>
        slot.field === field && slot.time === time,
      )

      if (!slot || slot.status === 'cancelled') {
        return 'available'
      }

      return slot.status
    },
  })),
)
